"""
情感分析配置文件
"""

class SentimentConfig:
    """情感分析配置类"""
    
    # 是否启用AI情感分析
    ENABLE_AI_SENTIMENT = True
    
    # 情感分析缓存过期时间（秒）
    SENTIMENT_CACHE_EXPIRE = 3600  # 1小时
    
    # 批量分析的最大文本数量
    MAX_BATCH_SIZE = 10
    
    # 单次分析的最大文本长度
    MAX_TEXT_LENGTH = 1000
    

    
    # AI情感分析的prompt模板（优化版本：适配 Firecrawl MCP）
    AI_SENTIMENT_PROMPT_TEMPLATE = """
请对以下文本进行情感分析，只返回一个词：positive（积极）、negative（消极）或neutral（中性）。

分析要求：
1. 考虑整体语境和语义
2. 理解反讽、双重否定等复杂表达
3. 综合评估整体情感倾向
4. 重点关注主要观点而非细节描述

文本内容：
{text}

请直接进行情感分析，最后只返回：positive、negative 或 neutral
"""
    
    # 情感分析结果映射
    SENTIMENT_MAPPING = {
        'positive': '积极',
        'negative': '消极', 
        'neutral': '中性'
    }
    


    # AI文章提取的prompt模板（集成Firecrawl MCP）
    AI_ARTICLE_EXTRACTION_PROMPT = """
你是一个专业的新闻文章提取助手，具备网络搜索能力。请严格按照以下要求从内容中提取结构化的文章信息。

**重要要求**：
1. 必须返回有效的JSON数组格式
2. 不要添加任何解释文字或markdown标记
3. 确保JSON格式正确，可以被程序解析

**网络搜索增强**：
- 可以使用firecrawl_search工具搜索相关主题的最新信息
- 可以使用firecrawl_scrape工具抓取特定网页内容
- 从搜索结果中获取真实的新闻文章URL和内容
- 验证和补充文章信息的准确性

**提取要求**：
1. 识别并提取所有独立的文章或新闻条目
2. 为每篇文章生成简洁准确的标题（不超过50字）
3. 提取文章的核心内容（200-500字）
4. 识别文章来源
5. **URL要求**：使用Firecrawl MCP工具获取真实可访问的新闻文章URL链接
   - 优先使用firecrawl_search搜索相关主题获取真实URL
   - 不要使用示例URL、占位符URL或虚构的链接
   - 确保每个URL都指向真实存在的网页内容
   - 优先提供权威媒体和官方信息源的链接
   - 如果确实无法获取真实URL，则设为空字符串
6. 设置发布时间为当前时间格式：YYYY-MM-DD HH:MM:SS

**严格的JSON格式**：
[
  {{
    "title": "文章标题",
    "content": "文章主要内容",
    "source": "来源网站或平台",
    "url": "真实可访问的完整URL地址",
    "publish_time": "2024-01-01 12:00:00"
  }}
]

**原始内容**：
{content}

**输出要求**：只返回JSON数组，开头必须是"["，结尾必须是"]"，不要包含任何其他文字。

**重要指令**：在提取文章信息前，请先使用firecrawl_search工具搜索相关主题，以获取最新、最准确的信息和真实URL。
"""

    @classmethod
    def get_ai_prompt(cls, text: str) -> str:
        """获取AI情感分析的prompt"""
        return cls.AI_SENTIMENT_PROMPT_TEMPLATE.format(text=text[:cls.MAX_TEXT_LENGTH])

    @classmethod
    def get_article_extraction_prompt(cls, content: str) -> str:
        """获取AI文章提取的prompt"""
        return cls.AI_ARTICLE_EXTRACTION_PROMPT.format(content=content[:5000])
    
    @classmethod
    def is_ai_enabled(cls) -> bool:
        """检查是否启用AI情感分析"""
        return cls.ENABLE_AI_SENTIMENT
