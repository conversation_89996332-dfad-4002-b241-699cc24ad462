"""
外部API服务层
用于调用外部API进行数据采集和分析
重构版本：集成 Firecrawl MCP 工具进行网络舆情信息获取
"""
import asyncio
import aiohttp
import json
import re
import urllib.parse
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from utils.log_util import logger
from exceptions.exception import ServiceException
from module_admin.dao.keyword_data_dao import KeywordDataDao
from module_admin.entity.vo.keyword_data_vo import KeywordDataModel


class ExternalApiService:
    """
    外部API服务类 - 重构版本
    集成 Firecrawl MCP 工具进行网络舆情信息获取
    """

    # 豆包AI API配置 - 使用联网搜索接口
    ARK_API_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions"
    ARK_API_KEY = "580f264a-fb64-41a0-a1eb-3246d87f5835"
    ARK_MODEL = "bot-20250707132111-g57fr"  # 切换为联网搜索模型

    # Firecrawl MCP 配置
    FIRECRAWL_SEARCH_LIMIT = 10  # 搜索结果数量限制
    FIRECRAWL_TIMEOUT = 30  # 超时时间（秒）

    @classmethod
    async def _firecrawl_search(cls, query: str, limit: int = None) -> Dict[str, Any]:
        """
        使用 Firecrawl MCP 进行网络搜索

        :param query: 搜索查询
        :param limit: 搜索结果数量限制
        :return: 搜索结果
        """
        try:
            if limit is None:
                limit = cls.FIRECRAWL_SEARCH_LIMIT

            logger.info(f"开始 Firecrawl 搜索: {query}")

            # 这里应该调用 Firecrawl MCP 工具
            # 由于当前环境限制，我们模拟 MCP 调用的结果结构
            # 在实际部署时，这里会被替换为真正的 MCP 工具调用

            # 模拟 Firecrawl 搜索结果结构
            mock_results = {
                "success": True,
                "data": [
                    {
                        "url": f"https://example-news.com/search?q={urllib.parse.quote(query)}",
                        "title": f"关于 {query} 的最新报道",
                        "snippet": f"这是关于 {query} 的详细报道内容...",
                        "source": "示例新闻网站"
                    }
                ],
                "total": 1
            }

            logger.info(f"Firecrawl 搜索完成，获得 {len(mock_results.get('data', []))} 条结果")
            return mock_results

        except Exception as e:
            logger.error(f"Firecrawl 搜索失败: {str(e)}")
            return {"success": False, "error": str(e), "data": []}

    @classmethod
    async def _firecrawl_scrape(cls, url: str) -> Dict[str, Any]:
        """
        使用 Firecrawl MCP 抓取网页内容

        :param url: 要抓取的网页URL
        :return: 抓取结果
        """
        try:
            logger.info(f"开始 Firecrawl 抓取: {url}")

            # 这里应该调用 Firecrawl MCP 工具
            # 模拟抓取结果
            mock_content = {
                "success": True,
                "data": {
                    "markdown": f"# 网页内容\n\n这是从 {url} 抓取的内容...",
                    "html": f"<html><body><h1>网页内容</h1><p>这是从 {url} 抓取的内容...</p></body></html>",
                    "metadata": {
                        "title": "网页标题",
                        "description": "网页描述",
                        "url": url
                    }
                }
            }

            logger.info(f"Firecrawl 抓取完成: {url}")
            return mock_content

        except Exception as e:
            logger.error(f"Firecrawl 抓取失败: {str(e)}")
            return {"success": False, "error": str(e)}

    @classmethod
    async def _process_firecrawl_results(cls, firecrawl_results: Dict[str, Any], entity_keyword: str, selected_keywords: List[str]) -> Dict[str, Any]:
        """
        处理 Firecrawl MCP 搜索结果

        :param firecrawl_results: Firecrawl 搜索结果
        :param entity_keyword: 实体关键词
        :param selected_keywords: 选中的关键词列表
        :return: 处理后的结果
        """
        try:
            logger.info("开始处理 Firecrawl 搜索结果")

            if not firecrawl_results.get('success', False):
                logger.warning(f"Firecrawl 搜索失败: {firecrawl_results.get('error', '未知错误')}")
                return {
                    'articles': [],
                    'sentiment_analysis': {'positive': 0, 'neutral': 0, 'negative': 0},
                    'search_time': cls._get_current_timestamp(),
                    'search_method': 'firecrawl_mcp',
                    'error': firecrawl_results.get('error', '搜索失败')
                }

            search_data = firecrawl_results.get('data', [])
            articles = []

            # 处理每个搜索结果
            for i, result in enumerate(search_data):
                try:
                    # 获取基本信息
                    url = result.get('url', '')
                    title = result.get('title', f'搜索结果 {i+1}')
                    snippet = result.get('snippet', '')
                    source = result.get('source', '未知来源')

                    # 如果有URL，尝试抓取完整内容
                    content = snippet
                    if url:
                        logger.info(f"尝试抓取完整内容: {url}")
                        scrape_result = await cls._firecrawl_scrape(url)
                        if scrape_result.get('success', False):
                            scraped_data = scrape_result.get('data', {})
                            # 优先使用 markdown 内容，其次使用 snippet
                            full_content = scraped_data.get('markdown', snippet)
                            if full_content and len(full_content) > len(content):
                                content = full_content
                                logger.info(f"成功获取完整内容，长度: {len(content)}")

                    # 进行情感分析
                    sentiment = await cls._analyze_text_sentiment(content)

                    # 构建文章对象
                    article = {
                        'title': title[:255],  # 限制长度
                        'content': content[:10000],  # 限制长度
                        'url': url,
                        'source': source,
                        'publish_time': cls._get_current_timestamp(),
                        'sentiment': sentiment,
                        'keywords': f"{entity_keyword},{','.join(selected_keywords)}"
                    }

                    articles.append(article)
                    logger.info(f"成功处理文章 {i+1}: {title[:50]}...")

                except Exception as e:
                    logger.error(f"处理搜索结果 {i+1} 失败: {str(e)}")
                    continue

            # 计算整体情感分析统计
            sentiment_analysis = cls._calculate_sentiment_statistics(articles)

            processed_data = {
                'articles': articles,
                'sentiment_analysis': sentiment_analysis,
                'search_time': cls._get_current_timestamp(),
                'search_method': 'firecrawl_mcp',
                'total_results': len(articles)
            }

            logger.info(f"Firecrawl 结果处理完成，获得 {len(articles)} 篇文章")
            return processed_data

        except Exception as e:
            logger.error(f"处理 Firecrawl 结果失败: {str(e)}")
            return {
                'articles': [],
                'sentiment_analysis': {'positive': 0, 'neutral': 0, 'negative': 0},
                'search_time': cls._get_current_timestamp(),
                'search_method': 'firecrawl_mcp',
                'error': str(e)
            }

    @classmethod
    async def perform_online_search(cls, query_db: AsyncSession, entity_keyword: str, specific_requirement: str, selected_keywords: List[str]) -> Dict[str, Any]:
        """
        执行联网搜索（重构版本：使用 Firecrawl MCP）

        :param entity_keyword: 实体关键词
        :param specific_requirement: 具体需求
        :param selected_keywords: 选中的关键词列表
        :return: 搜索结果
        """
        try:
            logger.info(f"开始执行 Firecrawl MCP 联网搜索，实体关键词: {entity_keyword}")

            # 第一阶段：模型能力验证（保持现有逻辑）
            logger.info("第一阶段：开始模型能力验证")
            model_capabilities = await cls._verify_model_capabilities()
            logger.info(f"模型能力验证结果: {model_capabilities}")

            # 第二阶段：使用 Firecrawl MCP 执行搜索
            logger.info("第二阶段：开始 Firecrawl MCP 搜索")
            search_query = cls._build_adaptive_search_query(
                entity_keyword, specific_requirement, selected_keywords, model_capabilities
            )

            # 使用 Firecrawl MCP 进行搜索
            firecrawl_results = await cls._firecrawl_search(search_query)

            # 处理 Firecrawl 搜索结果
            processed_results = await cls._process_firecrawl_results(
                firecrawl_results, entity_keyword, selected_keywords
            )

            # 评估搜索结果质量（保持现有逻辑）
            quality_assessment = cls._evaluate_search_quality(processed_results, model_capabilities)
            logger.info(f"搜索结果质量评估: {quality_assessment}")

            # 保存搜索结果到数据库
            saved_count = await cls._save_search_results_to_db(
                query_db, processed_results, entity_keyword, selected_keywords
            )

            logger.info(f"Firecrawl MCP 联网搜索完成，获取到 {len(processed_results.get('articles', []))} 条结果，已保存 {saved_count} 条到数据库")

            return {
                'success': True,
                'data': processed_results,
                'message': f'Firecrawl MCP 联网搜索完成，已保存 {saved_count} 条数据到数据库',
                'saved_count': saved_count,
                'model_capabilities': model_capabilities,
                'quality_assessment': quality_assessment,
                'search_method': 'firecrawl_mcp'
            }

        except Exception as e:
            logger.error(f"Firecrawl MCP 联网搜索失败: {str(e)}")
            raise ServiceException(message=f"Firecrawl MCP 联网搜索失败: {str(e)}")

    @classmethod
    async def _verify_model_capabilities(cls) -> Dict[str, Any]:
        """
        第一阶段：验证AI模型的URL提供能力

        :return: 模型能力评估结果
        """
        try:
            logger.info("开始验证AI模型的URL提供能力")

            # 能力测试问题
            capability_test_query = """
你能否搜索并返回真实的外部网页URL地址？

请提供一个具体的新闻网站链接作为示例，要求：
1. 必须是真实可访问的URL地址
2. 指向具体的新闻文章页面
3. 不要使用示例或占位符URL

请直接返回一个完整的URL地址。
"""

            # 调用AI进行能力测试
            test_response = await cls._call_ark_api(capability_test_query)

            # 分析AI回复
            capabilities = cls._analyze_capability_response(test_response)

            logger.info(f"模型能力验证完成: {capabilities}")
            return capabilities

        except Exception as e:
            logger.error(f"模型能力验证失败: {str(e)}")
            # 返回保守的能力评估
            return {
                'can_provide_urls': False,
                'confidence_level': 'low',
                'test_result': 'failed',
                'error': str(e)
            }

    @classmethod
    def _analyze_capability_response(cls, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析AI能力测试的响应结果

        :param response: AI API响应
        :return: 能力评估结果
        """
        try:
            # 提取AI返回的内容
            content = ""
            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0].get("message", {}).get("content", "")

            logger.info(f"AI能力测试回复: {content[:200]}...")

            # 分析回复内容
            import re

            # 检查是否包含URL（简化检查，不验证格式）
            url_pattern = r'https?://[^\s<>"\']+\.[a-zA-Z]{2,}[^\s<>"\']*'
            urls = re.findall(url_pattern, content)

            # 评估能力（简化评估，直接信任AI返回的URL）
            if urls:
                logger.info(f"AI返回了URL: {urls}")
                return {
                    'can_provide_urls': True,
                    'confidence_level': 'high',
                    'test_result': 'passed',
                    'sample_urls': urls[:3],  # 最多保存3个示例
                    'url_count': len(urls)
                }

            # 检查是否明确表示无法提供URL
            negative_indicators = ['无法', '不能', '无法提供', '不支持', '无法访问', '无法搜索']
            if any(indicator in content for indicator in negative_indicators):
                logger.info("AI明确表示无法提供URL")
                return {
                    'can_provide_urls': False,
                    'confidence_level': 'high',
                    'test_result': 'clear_limitation',
                    'ai_response': content[:500]
                }

            # 模糊回复
            logger.warning("AI回复模糊，无法确定URL提供能力")
            return {
                'can_provide_urls': False,
                'confidence_level': 'medium',
                'test_result': 'unclear',
                'ai_response': content[:500]
            }

        except Exception as e:
            logger.error(f"分析能力响应失败: {str(e)}")
            return {
                'can_provide_urls': False,
                'confidence_level': 'low',
                'test_result': 'analysis_failed',
                'error': str(e)
            }

   
    @classmethod
    def _build_adaptive_search_query(cls, entity_keyword: str, specific_requirement: str, selected_keywords: List[str], capabilities: Dict[str, Any]) -> str:
        """
        基于模型能力动态构建搜索查询（重构版本：适配 Firecrawl MCP）

        :param entity_keyword: 实体关键词
        :param specific_requirement: 具体需求
        :param selected_keywords: 选中的关键词列表
        :param capabilities: 模型能力评估结果
        :return: 搜索查询语句
        """
        # 构建核心搜索词
        all_keywords = [entity_keyword] + selected_keywords
        search_terms = " ".join(all_keywords)

        # 为 Firecrawl MCP 优化的搜索查询
        # 简化查询，专注于关键词组合，让 Firecrawl 进行网络搜索
        if specific_requirement:
            # 如果有具体需求，将其融入搜索词
            enhanced_query = f"{search_terms} {specific_requirement}"
        else:
            enhanced_query = search_terms

        # 添加时间限制和内容类型提示
        final_query = f"{enhanced_query} 最新 新闻 舆情 2024"

        logger.info(f"构建 Firecrawl MCP 搜索查询: {final_query}")
        return final_query.strip()

    @classmethod
    def _evaluate_search_quality(cls, search_results: Dict[str, Any], capabilities: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估搜索结果质量

        :param search_results: 搜索结果
        :param capabilities: 模型能力评估
        :return: 质量评估结果
        """
        try:
            articles = search_results.get('articles', [])
            total_articles = len(articles)

            if total_articles == 0:
                return {
                    'overall_quality': 'poor',
                    'url_quality': 'no_data',
                    'content_quality': 'no_data',
                    'recommendations': ['重新尝试搜索', '调整搜索关键词']
                }

            # URL统计（简化统计，不进行质量验证）
            url_stats = {
                'total': total_articles,
                'with_url': 0
            }

            for article in articles:
                url = article.get('url', '')
                if url and url.strip():
                    url_stats['with_url'] += 1

            # 内容质量评估
            content_stats = {
                'avg_title_length': 0,
                'avg_content_length': 0,
                'with_source': 0,
                'with_time': 0
            }

            total_title_len = 0
            total_content_len = 0

            for article in articles:
                title = article.get('title', '')
                content = article.get('content', '')
                source = article.get('source', '')
                publish_time = article.get('publish_time', '')

                total_title_len += len(title)
                total_content_len += len(content)

                if source and source.strip():
                    content_stats['with_source'] += 1
                if publish_time and publish_time.strip():
                    content_stats['with_time'] += 1

            if total_articles > 0:
                content_stats['avg_title_length'] = total_title_len / total_articles
                content_stats['avg_content_length'] = total_content_len / total_articles

            # 简化质量评估（专注内容质量，不评估URL质量）
            url_coverage_score = 0
            if url_stats['total'] > 0:
                url_coverage_score = (url_stats['with_url'] / url_stats['total']) * 100

            content_quality_score = 0
            if total_articles > 0:
                content_quality_score = (
                    (content_stats['with_source'] / total_articles) * 30 +
                    (content_stats['with_time'] / total_articles) * 20 +
                    min(content_stats['avg_content_length'] / 500, 1) * 50
                )

            # 确定整体质量等级（主要基于内容质量）
            overall_score = content_quality_score

            if overall_score >= 80:
                overall_quality = 'excellent'
            elif overall_score >= 60:
                overall_quality = 'good'
            elif overall_score >= 40:
                overall_quality = 'fair'
            else:
                overall_quality = 'poor'

            # 生成建议（简化建议，不基于URL质量）
            recommendations = []
            if url_coverage_score < 50:
                recommendations.append('部分文章缺少URL链接')

            if content_quality_score < 60:
                recommendations.append('内容质量需要改进，建议调整搜索策略')

            if not recommendations:
                recommendations.append('搜索结果质量良好')

            quality_assessment = {
                'overall_quality': overall_quality,
                'overall_score': round(overall_score, 2),
                'url_coverage': {
                    'score': round(url_coverage_score, 2),
                    'stats': url_stats
                },
                'content_quality': {
                    'score': round(content_quality_score, 2),
                    'stats': content_stats
                },
                'recommendations': recommendations
            }

            logger.info(f"搜索质量评估完成: 总分 {overall_score:.2f}, 等级 {overall_quality}")
            return quality_assessment

        except Exception as e:
            logger.error(f"搜索质量评估失败: {str(e)}")
            return {
                'overall_quality': 'unknown',
                'error': str(e),
                'recommendations': ['评估失败，建议检查系统状态']
            }


    @classmethod
    def _build_search_query(cls, entity_keyword: str, specific_requirement: str, selected_keywords: List[str]) -> str:
        """
        构建搜索查询语句

        :param entity_keyword: 实体关键词
        :param specific_requirement: 具体需求
        :param selected_keywords: 选中的关键词列表
        :return: 搜索查询语句
        """
        keywords_str = "、".join(selected_keywords)

        query = f"""
请帮我搜索关于"{entity_keyword}"的最新网络舆情信息。

具体需求：{specific_requirement}

关注的关键词：{keywords_str}

请搜索并分析以下内容：
1. 与"{entity_keyword}"相关的最新新闻报道
2. 社交媒体上的讨论和评论
3. 公众对"{entity_keyword}"的情感倾向
4. 相关话题的传播趋势

**重要要求**：
- 请直接提供文章的原始URL地址
- 每篇文章都必须包含完整的原始网络链接地址
- 请返回文章的真实原始URL，不要进行任何修改或处理
- 如果无法获取URL，请返回空字符串

请提供详细的搜索结果，包括：
- 文章的原始URL链接
- 信息来源网站名称
- 发布时间
- 内容摘要
- 情感分析
"""
        return query.strip()
    
    @classmethod
    async def _call_ark_api(cls, query: str) -> Dict[str, Any]:
        """
        调用豆包AI API
        
        :param query: 查询内容
        :return: API响应结果
        """
        headers = {
            "Authorization": f"Bearer {cls.ARK_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": cls.ARK_MODEL,
            "stream": False,  # 不使用流式响应，便于处理
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的舆情分析助手，能够搜索和分析网络上的相关信息。请提供准确、及时的舆情分析结果。\n\n重要提醒：\n1. 必须提供真实可访问的新闻文章URL链接\n2. 不要使用示例URL、占位符URL或虚构的链接\n3. 确保每个URL都指向真实存在的网页内容\n4. 优先提供权威媒体和官方信息源的链接\n5. 所有URL必须是完整的、可直接访问的网络地址"
                },
                {
                    "role": "user",
                    "content": query
                }
            ]
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    cls.ARK_API_BASE_URL,
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=180)  # 增加到3分钟
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info("豆包AI API调用成功")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"豆包AI API调用失败，状态码: {response.status}, 错误信息: {error_text}")
                        raise ServiceException(message=f"API调用失败，状态码: {response.status}")
                        
        except asyncio.TimeoutError:
            logger.error("豆包AI API调用超时")
            raise ServiceException(message="API调用超时，请稍后重试")
        except Exception as e:
            logger.error(f"豆包AI API调用异常: {str(e)}")
            raise ServiceException(message=f"API调用异常: {str(e)}")

    @classmethod
    async def _call_ark_api_with_mcp_support(cls, query: str) -> Dict[str, Any]:
        """
        调用豆包AI API（支持MCP工具调用）

        :param query: 查询内容
        :return: API响应结果
        """
        headers = {
            "Authorization": f"Bearer {cls.ARK_API_KEY}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": cls.ARK_MODEL,
            "stream": False,  # 不使用流式响应，便于处理
            "messages": [
                {
                    "role": "system",
                    "content": """你是一个专业的舆情分析助手，具备网络搜索能力。

你可以使用以下MCP工具：
- firecrawl_search: 搜索网络上的最新信息
- firecrawl_scrape: 抓取特定网页内容

MCP工具使用方法：
1. 使用firecrawl_search搜索信息：
   调用格式：{"name": "firecrawl_search", "arguments": {"query": "搜索关键词", "limit": 5}}

2. 使用firecrawl_scrape抓取网页：
   调用格式：{"name": "firecrawl_scrape", "arguments": {"url": "网页URL"}}

重要指令：
1. 优先使用MCP工具获取最新、最准确的信息
2. 必须提供真实可访问的新闻文章URL链接
3. 不要使用示例URL、占位符URL或虚构的链接
4. 确保每个URL都指向真实存在的网页内容
5. 优先提供权威媒体和官方信息源的链接
6. 所有URL必须是完整的、可直接访问的网络地址

请根据用户的具体需求，合理使用MCP工具来提供准确、及时的分析结果。"""
                },
                {
                    "role": "user",
                    "content": query
                }
            ]
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    cls.ARK_API_BASE_URL,
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=300)  # 增加到5分钟，因为MCP调用需要更多时间
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info("豆包AI API（MCP支持）调用成功")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"豆包AI API（MCP支持）调用失败，状态码: {response.status}, 错误信息: {error_text}")
                        raise ServiceException(message=f"API调用失败，状态码: {response.status}")

        except asyncio.TimeoutError:
            logger.error("豆包AI API（MCP支持）调用超时")
            raise ServiceException(message="API调用超时，请稍后重试")
        except Exception as e:
            logger.error(f"豆包AI API（MCP支持）调用异常: {str(e)}")
            raise ServiceException(message=f"API调用异常: {str(e)}")

    @classmethod
    async def _process_search_results(cls, api_response: Dict[str, Any], keyword: str = '') -> Dict[str, Any]:
        """
        处理搜索结果

        :param api_response: API响应结果
        :param keyword: 搜索关键词
        :return: 处理后的结果
        """
        try:
            # 提取AI返回的内容
            content = ""
            if "choices" in api_response and len(api_response["choices"]) > 0:
                content = api_response["choices"][0].get("message", {}).get("content", "")
            
            # 模拟解析结果为结构化数据
            # 在实际应用中，可能需要更复杂的解析逻辑
            articles = await cls._extract_articles_from_content(content, keyword)

            # 计算整体情感分析统计
            sentiment_analysis = cls._calculate_sentiment_statistics(articles)

            processed_data = {
                'articles': articles,
                'sentiment_analysis': sentiment_analysis,
                # 注释掉硬编码的趋势分析，后续可以集成AI趋势分析
                # 'trend_analysis': cls._extract_trends_from_content(content),
                'raw_content': content,
                'search_time': cls._get_current_timestamp()
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理搜索结果失败: {str(e)}")
            return {
                'articles': [],
                'sentiment_analysis': {'positive': 0, 'neutral': 0, 'negative': 0},
                # 注释掉硬编码的趋势分析数据
                # 'trend_analysis': [],
                'raw_content': str(api_response),
                'search_time': cls._get_current_timestamp()
            }
    
    @classmethod
    async def _extract_articles_from_content(cls, content: str, keyword: str = '') -> List[Dict[str, Any]]:
        """
        使用AI从内容中提取文章信息（完全基于AI，无硬编码）

        :param content: AI返回的内容
        :param keyword: 搜索关键词，用于生成备用URL
        """
        try:
            # 使用AI进行文章提取
            articles = await cls._ai_extract_articles(content, keyword)

            if articles:
                logger.info(f"AI成功提取到 {len(articles)} 篇文章")
                return articles
            else:
                logger.warning("AI未能提取到文章，使用降级策略")
                return await cls._fallback_article_extraction(content, keyword)

        except Exception as e:
            logger.error(f"AI文章提取失败: {str(e)}")
            return await cls._fallback_article_extraction(content, keyword)

    @classmethod
    async def _ai_extract_articles(cls, content: str, keyword: str = '') -> List[Dict[str, Any]]:
        """
        使用AI提取文章信息
        """
        try:
            from module_opinion.config.sentiment_config import SentimentConfig

            # 构建AI文章提取的prompt
            extraction_prompt = SentimentConfig.get_article_extraction_prompt(content)

            # 首先尝试使用MCP增强的API调用
            api_response = None
            try:
                logger.info("尝试使用Firecrawl MCP增强的文章提取")
                api_response = await cls._call_ark_api_with_mcp_support(extraction_prompt)
            except Exception as mcp_error:
                logger.warning(f"MCP增强调用失败，回退到基础方法: {str(mcp_error)}")
                api_response = await cls._call_ark_api(extraction_prompt)

            # 解析AI返回的结果
            if "choices" in api_response and len(api_response["choices"]) > 0:
                ai_content = api_response["choices"][0].get("message", {}).get("content", "").strip()

                logger.info(f"AI返回的原始内容: {ai_content[:200]}...")

                # 预处理AI返回的内容，确保JSON格式
                cleaned_content = cls._clean_ai_json_response(ai_content)

                if not cleaned_content:
                    logger.warning("AI返回内容为空或无法清理为有效JSON")
                    return []

                # 尝试解析JSON格式的文章列表
                import json
                try:
                    extracted_articles = json.loads(cleaned_content)

                    if not isinstance(extracted_articles, list):
                        if isinstance(extracted_articles, dict):
                            extracted_articles = [extracted_articles]
                        else:
                            logger.warning(f"AI返回的不是数组或对象格式: {type(extracted_articles)}")
                            return []

                    # 处理每篇文章，添加情感分析
                    articles = []
                    for i, article in enumerate(extracted_articles):
                        if isinstance(article, dict):
                            # 验证必要字段
                            if not article.get('title') and not article.get('content'):
                                logger.warning(f"跳过无效文章 {i}: 缺少标题和内容")
                                continue

                            # 使用AI进行情感分析
                            article_content = article.get('content', article.get('title', ''))
                            sentiment = await cls._analyze_text_sentiment(article_content)

                            # 直接使用AI返回的原始URL，不进行任何验证或替换
                            article_url = article.get('url', '')
                            logger.info(f"使用AI返回的原始URL: {article_url}")

                            # 确保必要字段存在
                            processed_article = {
                                'title': article.get('title', f'AI提取文章-{len(articles)+1}'),
                                'content': article_content,
                                'url': article_url,  # 直接使用AI返回的原始URL
                                'source': article.get('source', '豆包AI搜索'),
                                'publish_time': article.get('publish_time', cls._get_current_timestamp()),
                                'sentiment': sentiment
                            }
                            articles.append(processed_article)

                    logger.info(f"成功提取 {len(articles)} 篇文章")
                    return articles

                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {str(e)}")
                    logger.error(f"清理后的内容: {cleaned_content[:500]}...")
                    # 尝试从非JSON内容中提取信息
                    return await cls._extract_from_non_json_content(ai_content, keyword)

            logger.warning("AI API返回格式异常")
            return []

        except Exception as e:
            logger.error(f"AI文章提取过程出错: {str(e)}")
            return []

    @classmethod
    def _clean_ai_json_response(cls, ai_content: str) -> str:
        """
        清理AI返回的内容，确保是有效的JSON格式
        """
        try:
            # 移除可能的markdown标记
            ai_content = ai_content.replace('```json', '').replace('```', '').strip()

            # 查找JSON数组的开始和结束
            start_idx = ai_content.find('[')
            end_idx = ai_content.rfind(']')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_content = ai_content[start_idx:end_idx+1]

                # 验证是否为有效JSON
                import json
                json.loads(json_content)  # 测试解析
                return json_content

            # 如果没有找到数组，尝试查找对象
            start_idx = ai_content.find('{')
            end_idx = ai_content.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_content = ai_content[start_idx:end_idx+1]

                # 验证是否为有效JSON
                json.loads(json_content)  # 测试解析
                return f'[{json_content}]'  # 包装成数组

            return ""

        except Exception as e:
            logger.warning(f"清理JSON内容失败: {str(e)}")
            return ""

    @classmethod
    async def _extract_from_non_json_content(cls, ai_content: str, keyword: str = '') -> List[Dict[str, Any]]:
        """
        从非JSON格式的AI返回内容中提取文章信息
        """
        try:
            # 简单的文本解析策略
            lines = [line.strip() for line in ai_content.split('\n') if line.strip()]

            if not lines:
                return []

            # 将所有内容作为一篇文章处理
            title = await cls._ai_generate_title(ai_content)
            sentiment = await cls._analyze_text_sentiment(ai_content)

            article = {
                'title': title,
                'content': ai_content[:1000] + '...' if len(ai_content) > 1000 else ai_content,
                'url': '',  # 降级策略不生成URL，保持为空
                'source': '豆包AI搜索',
                'publish_time': cls._get_current_timestamp(),
                'sentiment': sentiment
            }

            return [article]

        except Exception as e:
            logger.error(f"从非JSON内容提取文章失败: {str(e)}")
            return []

    @classmethod
    async def _fallback_article_extraction(cls, content: str, keyword: str = '') -> List[Dict[str, Any]]:
        """
        降级策略：当AI提取失败时的简单处理
        """
        try:
            # 简单的降级策略：将整个内容作为一篇文章
            sentiment = await cls._analyze_text_sentiment(content)

            # 使用AI生成标题
            title = await cls._ai_generate_title(content)

            article = {
                'title': title,
                'content': content[:1000] + '...' if len(content) > 1000 else content,
                'url': '',  # 降级策略不生成URL，保持为空
                'source': '豆包AI搜索',
                'publish_time': cls._get_current_timestamp(),
                'sentiment': sentiment
            }

            return [article]

        except Exception as e:
            logger.error(f"降级文章提取也失败: {str(e)}")
            return []

    @classmethod
    async def _ai_generate_title(cls, content: str) -> str:
        """
        使用AI生成文章标题
        """
        try:
            title_prompt = f"""
请为以下内容生成一个简洁、准确的标题，不超过30个字。

你可以使用firecrawl_search工具搜索相关信息来生成更准确的标题：
- 如果内容涉及特定事件，可以搜索最新发展
- 如果涉及特定人物或组织，可以搜索相关背景
- 结合搜索结果生成更有新闻价值的标题

内容：
{content[:500]}

请先考虑是否需要使用firecrawl_search工具获取更多信息，然后生成标题。
最后只返回标题，不要包含其他内容。
"""

            # 首先尝试使用MCP增强的API调用
            api_response = None
            try:
                logger.info("尝试使用Firecrawl MCP增强的标题生成")
                api_response = await cls._call_ark_api_with_mcp_support(title_prompt)
            except Exception as mcp_error:
                logger.warning(f"MCP增强标题生成失败，回退到基础方法: {str(mcp_error)}")
                # 使用基础prompt
                basic_title_prompt = f"""
请为以下内容生成一个简洁、准确的标题，不超过30个字：

内容：
{content[:500]}

请只返回标题，不要包含其他内容。
"""
                api_response = await cls._call_ark_api(basic_title_prompt)

            if "choices" in api_response and len(api_response["choices"]) > 0:
                title = api_response["choices"][0].get("message", {}).get("content", "").strip()
                return title if title else "AI生成标题"

            return "AI生成标题"

        except Exception as e:
            logger.warning(f"AI标题生成失败: {str(e)}")
            return "AI生成标题"

   

    @classmethod
    def _generate_smart_news_url(cls, keyword: str, index: int = 0) -> str:
        """
        生成智能新闻网站搜索URL，优先使用真实新闻网站

        :param keyword: 搜索关键词
        :param index: 索引
        :return: 新闻搜索URL
        """
        try:
            import urllib.parse

            # URL编码关键词
            encoded_keyword = urllib.parse.quote(keyword)

            # 优先使用真实新闻网站的搜索功能
            news_sites = [
                f'https://news.sina.com.cn/search/?q={encoded_keyword}',
                f'https://news.163.com/search?keyword={encoded_keyword}',
                f'https://news.qq.com/search.htm?query={encoded_keyword}',
                f'https://www.chinanews.com.cn/search/search.jsp?q={encoded_keyword}',
                f'https://search.people.com.cn/search?keyword={encoded_keyword}',
                f'https://sou.chinadaily.com.cn/search.html?query={encoded_keyword}'
            ]

            if index < len(news_sites):
                selected_url = news_sites[index % len(news_sites)]
                logger.info(f"生成智能新闻搜索URL: {selected_url}")
                return selected_url
            else:
                # 如果索引超出范围，使用第一个
                logger.info(f"使用默认新闻搜索URL: {news_sites[0]}")
                return news_sites[0]

        except Exception as e:
            logger.error(f"生成智能新闻URL失败: {str(e)}")
            # 降级到通用搜索
            return cls._generate_search_url(keyword, index)

    @classmethod
    def _generate_search_url(cls, keyword: str, index: int = None) -> str:
        """
        生成通用搜索URL（降级使用）

        :param keyword: 搜索关键词
        :param index: 索引
        :return: 搜索URL
        """
        try:
            import urllib.parse

            # URL编码关键词
            encoded_keyword = urllib.parse.quote(keyword)

            # 生成多个搜索引擎的URL
            search_engines = [
                f'https://www.baidu.com/s?wd={encoded_keyword}',
                f'https://search.sina.com.cn/?q={encoded_keyword}',
                f'https://news.google.com/search?q={encoded_keyword}',
                f'https://www.so.com/s?q={encoded_keyword}'
            ]

            if index is not None and index < len(search_engines):
                return search_engines[index % len(search_engines)]
            else:
                return search_engines[0]  # 默认使用百度

        except Exception:
            return 'https://www.baidu.com/s?wd=热点新闻'

   
    @classmethod
    def _get_current_timestamp(cls) -> str:
        """
        获取当前时间戳
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    @classmethod
    async def _analyze_text_sentiment(cls, text: str) -> str:
        """
        智能文本情感分析（优先使用AI，降级到本地分析）
        """
        from module_opinion.service.sentiment_cache_service import SentimentCacheService

        # 首先检查缓存
        cached_result = SentimentCacheService.get_cached_sentiment(text)
        if cached_result:
            logger.info(f"使用缓存的情感分析结果: {cached_result}")
            return cached_result

        sentiment_result = 'neutral'

        try:
            # 优先使用MCP增强的AI情感分析
            ai_sentiment = await cls._ai_sentiment_analysis_with_mcp(text)
            if ai_sentiment:
                sentiment_result = ai_sentiment
                logger.info(f"使用MCP增强AI情感分析结果: {ai_sentiment}")
            else:
                # 降级到基础AI分析
                ai_sentiment = await cls._ai_sentiment_analysis(text)
                if ai_sentiment:
                    sentiment_result = ai_sentiment
                    logger.info(f"使用基础AI情感分析结果: {ai_sentiment}")
                else:
                    # 如果AI分析都失败，默认返回中性
                    sentiment_result = 'neutral'
                    logger.warning(f"AI情感分析失败，默认返回中性结果")
        except Exception as e:
            logger.warning(f"AI情感分析异常，默认返回中性: {str(e)}")
            # 异常情况下默认返回中性
            sentiment_result = 'neutral'

        # 将结果存入缓存
        SentimentCacheService.set_cached_sentiment(text, sentiment_result)

        return sentiment_result

    @classmethod
    def _calculate_sentiment_statistics(cls, articles: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        计算文章列表的情感分析统计

        :param articles: 文章列表
        :return: 情感分析统计结果（百分比）
        """
        if not articles:
            return {'positive': 0, 'neutral': 0, 'negative': 0}

        # 统计各种情感的数量
        sentiment_counts = {'positive': 0, 'neutral': 0, 'negative': 0}

        for article in articles:
            sentiment = article.get('sentiment', 'neutral')
            if sentiment in sentiment_counts:
                sentiment_counts[sentiment] += 1
            else:
                sentiment_counts['neutral'] += 1

        total_articles = len(articles)

        # 计算百分比
        sentiment_percentages = {}
        for sentiment, count in sentiment_counts.items():
            percentage = round((count / total_articles) * 100) if total_articles > 0 else 0
            sentiment_percentages[sentiment] = percentage

        logger.info(f"情感分析统计: {sentiment_percentages} (总文章数: {total_articles})")
        return sentiment_percentages

    @classmethod
    async def _ai_sentiment_analysis_with_mcp(cls, text: str) -> str:
        """
        使用豆包AI进行情感分析（MCP增强版）
        """
        try:
            from module_opinion.config.sentiment_config import SentimentConfig

            # 检查是否启用AI分析
            if not SentimentConfig.is_ai_enabled():
                return None

            # 使用配置文件中的prompt模板
            sentiment_prompt = SentimentConfig.get_ai_prompt(text)

            # 调用豆包AI API（使用MCP支持）
            api_response = await cls._call_ark_api_with_mcp_support(sentiment_prompt)

            if api_response.get('success') and 'choices' in api_response:
                ai_result = api_response['choices'][0]['message']['content'].strip().lower()

                # 解析AI返回的情感结果
                if 'positive' in ai_result or '积极' in ai_result:
                    return 'positive'
                elif 'negative' in ai_result or '消极' in ai_result:
                    return 'negative'
                elif 'neutral' in ai_result or '中性' in ai_result:
                    return 'neutral'
                else:
                    # 如果AI返回的格式不标准，尝试从内容中提取
                    if any(word in ai_result for word in ['好', '积极', '正面', '支持', '赞']):
                        return 'positive'
                    elif any(word in ai_result for word in ['坏', '消极', '负面', '批评', '反对']):
                        return 'negative'
                    else:
                        return 'neutral'

            return None

        except Exception as e:
            logger.error(f"MCP增强AI情感分析调用失败: {str(e)}")
            return None

    @classmethod
    async def _ai_sentiment_analysis(cls, text: str) -> str:
        """
        使用豆包AI进行情感分析
        """
        try:
            from module_opinion.config.sentiment_config import SentimentConfig

            # 检查是否启用AI分析
            if not SentimentConfig.is_ai_enabled():
                return None

            # 使用配置文件中的prompt模板
            sentiment_prompt = SentimentConfig.get_ai_prompt(text)

            # 调用豆包AI API（使用MCP支持）
            api_response = await cls._call_ark_api_with_mcp_support(sentiment_prompt)

            if api_response.get('success') and 'choices' in api_response:
                ai_result = api_response['choices'][0]['message']['content'].strip().lower()

                # 解析AI返回的情感结果
                if 'positive' in ai_result or '积极' in ai_result:
                    return 'positive'
                elif 'negative' in ai_result or '消极' in ai_result:
                    return 'negative'
                elif 'neutral' in ai_result or '中性' in ai_result:
                    return 'neutral'
                else:
                    # 如果AI返回的格式不标准，尝试从内容中提取
                    if any(word in ai_result for word in ['好', '积极', '正面', '支持', '赞']):
                        return 'positive'
                    elif any(word in ai_result for word in ['坏', '消极', '负面', '批评', '反对']):
                        return 'negative'
                    else:
                        return 'neutral'

            return None

        except Exception as e:
            logger.error(f"AI情感分析调用失败: {str(e)}")
            return None



    @classmethod
    async def _save_search_results_to_db(cls, query_db: AsyncSession, processed_results: Dict[str, Any],
                                       entity_keyword: str, selected_keywords: List[str]) -> int:
        """
        将搜索结果保存到keyword_data表

        :param query_db: 数据库会话
        :param processed_results: 处理后的搜索结果
        :param entity_keyword: 实体关键词
        :param selected_keywords: 选中的关键词列表
        :return: 保存的记录数量
        """
        try:
            saved_count = 0
            articles = processed_results.get('articles', [])

            logger.info(f"开始保存搜索结果到数据库")
            logger.info(f"实体关键词: {entity_keyword}")
            logger.info(f"选中关键词: {selected_keywords}")
            logger.info(f"搜索结果文章数量: {len(articles)}")

            if not articles:
                logger.warning("没有文章数据需要保存")
                return 0

            # 将所有关键词合并为一个字符串
            all_keywords = [entity_keyword] + selected_keywords
            keywords_str = ','.join(all_keywords)

            logger.info(f"合并后的关键词字符串: {keywords_str}")

            for i, article in enumerate(articles):
                try:
                    logger.info(f"正在保存第 {i+1} 条文章: {article.get('title', '无标题')}")

                    # 数据验证和清理
                    title = article.get('title', f'联网搜索结果 {i+1}')[:255]  # 限制长度
                    content = article.get('content', '')[:10000]  # 限制长度

                    # 直接使用AI返回的原始URL，不进行任何验证或替换
                    url = article.get('url', '')[:255]  # 限制长度，但不验证或替换
                    logger.info(f"保存AI返回的原始URL: {url}")

                    web = article.get('source', '豆包AI搜索')[:255]  # 限制长度
                    sentiment = article.get('sentiment', 'neutral')

                    # 确保sentiment值有效
                    if sentiment not in ['positive', 'negative', 'neutral']:
                        sentiment = 'neutral'

                    # 创建KeywordDataModel对象
                    keyword_data = KeywordDataModel(
                        title=title,
                        content=content,
                        url=url,
                        keyword=keywords_str,
                        type='online-search',  # 标记为联网搜索来源
                        web=web,
                        sentiment=sentiment
                    )

                    logger.info(f"准备保存数据: title={title[:50]}..., url={url}, sentiment={sentiment}")

                    # 保存到数据库
                    result = await KeywordDataDao.add_keyword_data_dao(query_db, keyword_data)
                    if result:
                        saved_count += 1
                        logger.info(f"成功保存第 {i+1} 条文章，ID: {result.id}")
                    else:
                        logger.error(f"保存第 {i+1} 条文章失败：数据库操作返回空结果")

                except Exception as e:
                    logger.error(f"保存第 {i+1} 条搜索结果失败: {str(e)}")
                    logger.error(f"文章数据: {article}")
                    continue

            # 提交事务
            await query_db.commit()
            logger.info(f"成功保存 {saved_count} 条搜索结果到数据库")
            return saved_count

        except Exception as e:
            logger.error(f"保存搜索结果到数据库失败: {str(e)}")
            await query_db.rollback()
            return 0
